// Rename Helper Script
// This script helps users rename downloaded files to the suggested timestamps

// This would be injected into a helper page to assist with renaming
class RenameHelper {
  constructor() {
    this.downloadHistory = [];
    this.loadDownloadHistory();
  }

  async loadDownloadHistory() {
    try {
      const result = await chrome.storage.local.get(['downloadHistory']);
      this.downloadHistory = result.downloadHistory || [];
      this.displayRenameInstructions();
    } catch (error) {
      console.error('Error loading download history:', error);
    }
  }

  displayRenameInstructions() {
    if (this.downloadHistory.length === 0) {
      console.log('No download history found.');
      return;
    }

    console.log('=== RENAME INSTRUCTIONS ===');
    console.log('The following files were downloaded and should be renamed:');
    console.log('');

    this.downloadHistory.forEach((item, index) => {
      if (item.status === 'completed') {
        console.log(`${index + 1}. Original: ${item.downloadPath}`);
        console.log(`   Rename to: ${item.filename}`);
        console.log(`   Prompt: ${item.prompt.substring(0, 50)}...`);
        console.log('');
      }
    });

    console.log('=== BATCH RENAME COMMANDS ===');
    console.log('You can use these commands in your terminal/command prompt:');
    console.log('');

    // Generate batch rename commands for different operating systems
    this.generateBatchCommands();
  }

  generateBatchCommands() {
    const recentDownloads = this.downloadHistory
      .filter(item => item.status === 'completed')
      .slice(-10); // Last 10 downloads

    if (recentDownloads.length === 0) return;

    console.log('Windows (Command Prompt):');
    recentDownloads.forEach(item => {
      const oldName = item.downloadPath.split('/').pop();
      const newName = item.filename;
      console.log(`ren "${oldName}" "${newName}"`);
    });

    console.log('');
    console.log('Windows (PowerShell):');
    recentDownloads.forEach(item => {
      const oldName = item.downloadPath.split('/').pop();
      const newName = item.filename;
      console.log(`Rename-Item "${oldName}" "${newName}"`);
    });

    console.log('');
    console.log('Mac/Linux:');
    recentDownloads.forEach(item => {
      const oldName = item.downloadPath.split('/').pop();
      const newName = item.filename;
      console.log(`mv "${oldName}" "${newName}"`);
    });
  }

  async clearHistory() {
    try {
      await chrome.storage.local.set({ downloadHistory: [] });
      this.downloadHistory = [];
      console.log('Download history cleared.');
    } catch (error) {
      console.error('Error clearing history:', error);
    }
  }
}

// Export for use in popup or other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = RenameHelper;
} else if (typeof window !== 'undefined') {
  window.RenameHelper = RenameHelper;
}
