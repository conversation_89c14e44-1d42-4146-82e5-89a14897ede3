/* Auto Image Generator - Popup Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #1a1a1a;
    color: #ffffff;
    width: 400px;
    min-height: 600px;
    overflow-x: hidden;
}

.container {
    padding: 0;
    display: flex;
    flex-direction: column;
    min-height: 600px;
}

/* Header */
.header {
    background: linear-gradient(135deg, #0066cc, #004499);
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #333;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo svg {
    color: #ffffff;
}

.logo h1 {
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
}

.version {
    font-size: 12px;
    color: #cccccc;
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 8px;
    border-radius: 12px;
}

/* Sections */
.section {
    padding: 16px 20px;
    border-bottom: 1px solid #333;
}

.section:last-child {
    border-bottom: none;
}

.section h2 {
    font-size: 14px;
    font-weight: 600;
    color: #cccccc;
    margin-bottom: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* File Upload */
.file-upload-area {
    border: 2px dashed #444;
    border-radius: 8px;
    padding: 24px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #222;
}

.file-upload-area:hover {
    border-color: #0066cc;
    background: #252525;
}

.file-upload-area.dragover {
    border-color: #0066cc;
    background: #1a2332;
}

.upload-icon {
    font-size: 32px;
    margin-bottom: 8px;
}

.upload-text p {
    margin-bottom: 4px;
    color: #ffffff;
}

.upload-link {
    color: #0066cc;
    text-decoration: underline;
    cursor: pointer;
}

.upload-text small {
    color: #888;
    font-size: 12px;
}

.file-info {
    background: #2a2a2a;
    border-radius: 6px;
    padding: 12px;
    margin-top: 12px;
}

.file-name {
    font-weight: 600;
    color: #0066cc;
    margin-bottom: 4px;
}

.file-details {
    font-size: 12px;
    color: #888;
}

/* Status */
.status-display {
    background: #2a2a2a;
    border-radius: 6px;
    padding: 12px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-dot.idle {
    background: #666;
    animation: none;
}

.status-dot.running {
    background: #0066cc;
}

.status-dot.error {
    background: #ff4444;
}

.status-dot.completed {
    background: #00cc66;
    animation: none;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.status-message {
    font-size: 12px;
    color: #888;
}

/* Progress */
.progress-container {
    margin-bottom: 12px;
}

.progress-bar {
    background: #333;
    border-radius: 4px;
    height: 8px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    background: linear-gradient(90deg, #0066cc, #0088ff);
    height: 100%;
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #888;
}

.current-prompt {
    background: #2a2a2a;
    border-radius: 4px;
    padding: 8px;
    font-size: 12px;
    margin-top: 8px;
}

.current-prompt strong {
    color: #0066cc;
}

#currentPromptText {
    color: #ccc;
    display: block;
    margin-top: 4px;
    word-break: break-word;
    max-height: 60px;
    overflow-y: auto;
}

/* Controls */
.controls {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.btn {
    flex: 1;
    min-width: 100px;
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: #0066cc;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #0055aa;
}

.btn-secondary {
    background: #666;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #555;
}

.btn-danger {
    background: #cc4444;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: #aa3333;
}

.btn-icon {
    font-size: 12px;
}

/* Log */
.log-container {
    background: #222;
    border-radius: 6px;
    max-height: 120px;
    overflow-y: auto;
    padding: 8px;
}

.log-entry {
    display: flex;
    gap: 8px;
    padding: 4px 0;
    font-size: 12px;
    border-bottom: 1px solid #333;
}

.log-entry:last-child {
    border-bottom: none;
}

.log-time {
    color: #666;
    min-width: 60px;
    font-family: monospace;
}

.log-message {
    color: #ccc;
    flex: 1;
}

.log-entry.error .log-message {
    color: #ff6666;
}

.log-entry.success .log-message {
    color: #66ff66;
}

.log-entry.info .log-message {
    color: #66ccff;
}

/* Footer */
.footer {
    margin-top: auto;
    padding: 12px 20px;
    border-top: 1px solid #333;
    background: #1a1a1a;
}

.footer-links {
    display: flex;
    gap: 16px;
    margin-bottom: 8px;
}

.footer-links a {
    color: #0066cc;
    text-decoration: none;
    font-size: 12px;
}

.footer-links a:hover {
    text-decoration: underline;
}

.footer-text {
    font-size: 11px;
    color: #666;
    text-align: center;
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #333;
}

::-webkit-scrollbar-thumb {
    background: #666;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #777;
}
