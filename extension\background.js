// Auto Image Generator - Background Script
// Handles JSON processing, download management, and coordination

class BackgroundManager {
  constructor() {
    this.downloadQueue = new Map();
    this.automationStatus = {
      isRunning: false,
      currentIndex: 0,
      totalPrompts: 0,
      status: 'idle',
      message: ''
    };

    this.setupMessageListener();
    this.setupDownloadListener();
    this.loadPersistedData();
    this.log('Background manager initialized');
  }

  async loadPersistedData() {
    try {
      const result = await chrome.storage.local.get([
        'lastPrompts',
        'automationStatus',
        'downloadHistory'
      ]);

      if (result.lastPrompts) {
        this.lastPrompts = result.lastPrompts;
        this.log(`Loaded ${result.lastPrompts.length} prompts from storage`);
      }

      if (result.automationStatus && !result.automationStatus.isRunning) {
        this.automationStatus = { ...this.automationStatus, ...result.automationStatus };
      }

      if (result.downloadHistory) {
        this.log(`Loaded ${result.downloadHistory.length} download history entries`);
      }

    } catch (error) {
      this.log(`Error loading persisted data: ${error.message}`);
    }
  }

  async saveToStorage(key, data) {
    try {
      await chrome.storage.local.set({ [key]: data });
    } catch (error) {
      this.log(`Error saving to storage: ${error.message}`);
    }
  }

  log(message) {
    console.log(`[Background] ${message}`);
  }

  setupMessageListener() {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // Keep message channel open for async response
    });
  }

  setupDownloadListener() {
    // Listen for download determination to suggest filename
    if (chrome.downloads.onDeterminingFilename) {
      chrome.downloads.onDeterminingFilename.addListener((downloadItem, suggest) => {
        this.handleFilenameRequest(downloadItem, suggest);
      });
    }

    chrome.downloads.onCreated.addListener((downloadItem) => {
      this.handleDownloadCreated(downloadItem);
    });

    chrome.downloads.onChanged.addListener((downloadDelta) => {
      this.handleDownloadChanged(downloadDelta);
    });
  }

  handleFilenameRequest(downloadItem, suggest) {
    // Check if this is one of our downloads
    const queueEntry = Array.from(this.downloadQueue.values()).find(entry =>
      entry.status === 'pending'
    );

    if (queueEntry && queueEntry.suggestedFilename) {
      this.log(`Suggesting filename: ${queueEntry.suggestedFilename}`);
      suggest({ filename: queueEntry.suggestedFilename });
    } else {
      // Let Chrome handle the filename normally
      suggest();
    }
  }

  async handleMessage(request, sender, sendResponse) {
    try {
      switch (request.action) {
        case 'processJsonFile':
          const prompts = await this.processJsonFile(request.fileContent);
          sendResponse({ success: true, prompts });
          break;

        case 'startAutomation':
          await this.startAutomation(request.prompts, request.saveMethod);
          sendResponse({ success: true });
          break;

        case 'stopAutomation':
          await this.stopAutomation();
          sendResponse({ success: true });
          break;

        case 'getStatus':
          sendResponse({
            success: true,
            status: this.automationStatus,
            lastPrompts: this.lastPrompts || []
          });
          break;

        case 'statusUpdate':
          this.updateStatus(request.status, request.message, request.data);
          this.broadcastStatusUpdate();
          sendResponse({ success: true });
          break;

        case 'downloadStarted':
          this.handleDownloadStarted(request.promptData, request.timestamp, request.suggestedFilename);
          sendResponse({ success: true });
          break;

        case 'debugLog':
          this.handleDebugLog(request.message, request.level, request.timestamp);
          sendResponse({ success: true });
          break;

        default:
          sendResponse({ success: false, error: 'Unknown action' });
      }
    } catch (error) {
      this.log(`Error handling message: ${error.message}`);
      sendResponse({ success: false, error: error.message });
    }
  }

  async processJsonFile(fileContent) {
    try {
      this.log('Processing JSON file...');

      // Parse JSON content
      let jsonData;
      try {
        jsonData = JSON.parse(fileContent);
      } catch (parseError) {
        throw new Error('Invalid JSON format');
      }

      // Handle both single object and array formats
      const dataArray = Array.isArray(jsonData) ? jsonData : [jsonData];

      // Extract visual prompts
      const prompts = [];
      for (let i = 0; i < dataArray.length; i++) {
        const item = dataArray[i];

        if (!item.visual_prompt) {
          this.log(`Warning: Item ${i} missing visual_prompt field`);
          continue;
        }

        prompts.push({
          id: item.id || `prompt_${i}`,
          ori_text: item.ori_text || '',
          duration: item.duration || '',
          visual_prompt: item.visual_prompt
        });
      }

      if (prompts.length === 0) {
        throw new Error('No valid prompts found in JSON file');
      }

      // Save prompts to persistent storage
      this.lastPrompts = prompts;
      await this.saveToStorage('lastPrompts', prompts);

      this.log(`Extracted ${prompts.length} prompts from JSON`);
      return prompts;

    } catch (error) {
      this.log(`JSON processing error: ${error.message}`);
      throw error;
    }
  }

  async startAutomation(prompts, saveMethod = 'download') {
    if (this.automationStatus.isRunning) {
      throw new Error('Automation is already running');
    }

    this.updateStatus('starting', `Initializing automation (${saveMethod} mode)...`, {
      totalPrompts: prompts.length,
      currentIndex: 0,
      saveMethod: saveMethod
    });

    try {
      // Validate prompts
      if (!prompts || prompts.length === 0) {
        throw new Error('No prompts provided');
      }

      // Check for active tab
      const tabs = await chrome.tabs.query({
        active: true,
        currentWindow: true
      });

      if (tabs.length === 0) {
        throw new Error('No active tab found. Please open the target page first.');
      }

      const tab = tabs[0];

      // Verify tab is loaded and accessible
      try {
        await chrome.tabs.get(tab.id);
      } catch (tabError) {
        throw new Error('Target tab is not accessible. Please refresh the page.');
      }

      // Inject content script first
      try {
        await chrome.scripting.executeScript({
          target: { tabId: tab.id },
          files: ['content.js']
        });

        // Wait a moment for content script to initialize
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (injectionError) {
        throw new Error('Failed to inject content script. Please refresh the page and try again.');
      }

      // Send message to content script with timeout
      const response = await this.sendMessageWithTimeout(tab.id, {
        action: 'startAutomation',
        prompts: prompts,
        saveMethod: saveMethod
      }, 10000);

      if (!response || !response.success) {
        throw new Error(response?.error || 'Content script did not respond properly');
      }

      this.updateStatus('running', 'Automation started successfully', {
        totalPrompts: prompts.length,
        currentIndex: 0
      });

    } catch (error) {
      this.updateStatus('error', `Failed to start automation: ${error.message}`);
      throw error;
    }
  }

  async sendMessageWithTimeout(tabId, message, timeout = 5000) {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error('Message timeout - content script may not be loaded'));
      }, timeout);

      try {
        chrome.tabs.sendMessage(tabId, message, (response) => {
          clearTimeout(timer);
          if (chrome.runtime.lastError) {
            reject(new Error(`Communication error: ${chrome.runtime.lastError.message}`));
          } else {
            resolve(response);
          }
        });
      } catch (error) {
        clearTimeout(timer);
        reject(new Error(`Failed to send message: ${error.message}`));
      }
    });
  }

  async stopAutomation() {
    try {
      const tabs = await chrome.tabs.query({
        active: true,
        currentWindow: true
      });

      if (tabs.length > 0) {
        try {
          await this.sendMessageWithTimeout(tabs[0].id, {
            action: 'stopAutomation'
          }, 3000);
        } catch (error) {
          this.log(`Error stopping automation: ${error.message}`);
          // Continue anyway - we'll update status regardless
        }
      }
    } catch (error) {
      this.log(`Error querying tabs: ${error.message}`);
    }

    this.updateStatus('stopped', 'Automation stopped');
  }

  updateStatus(status, message, data = {}) {
    this.automationStatus = {
      ...this.automationStatus,
      status,
      message,
      isRunning: status === 'running' || status === 'processing',
      ...data
    };

    // Save status to storage (but don't persist running state)
    const statusToSave = { ...this.automationStatus };
    if (statusToSave.isRunning) {
      statusToSave.isRunning = false;
      statusToSave.status = 'idle';
    }
    this.saveToStorage('automationStatus', statusToSave);

    this.log(`Status update: ${status} - ${message}`);
  }

  broadcastStatusUpdate() {
    // Send status update to popup if it's open
    chrome.runtime.sendMessage({
      action: 'statusBroadcast',
      status: this.automationStatus
    }).catch(() => {
      // Popup might not be open, ignore error
    });
  }

  handleDownloadStarted(promptData, timestamp, suggestedFilename) {
    const downloadId = `${promptData.id}_${timestamp}`;
    this.downloadQueue.set(downloadId, {
      promptData,
      timestamp,
      suggestedFilename,
      status: 'pending'
    });

    this.log(`Download queued for prompt: ${promptData.id} -> ${suggestedFilename}`);
  }

  handleDownloadCreated(downloadItem) {
    this.log(`Download created: ${downloadItem.filename}`);

    // Check if this is one of our downloads
    const queueEntry = Array.from(this.downloadQueue.values()).find(entry =>
      entry.status === 'pending'
    );

    if (queueEntry) {
      queueEntry.downloadId = downloadItem.id;
      queueEntry.status = 'downloading';
      queueEntry.originalFilename = downloadItem.filename;

      // Immediately suggest a new filename
      this.suggestNewFilename(downloadItem.id, queueEntry);
    }
  }

  async suggestNewFilename(downloadId, queueEntry) {
    try {
      const timestamp = new Date(queueEntry.timestamp);
      const formattedTimestamp = this.formatTimestamp(timestamp);
      const promptId = queueEntry.promptData.id || 'unknown';

      // Get file extension from original filename
      const extension = this.getFileExtension(queueEntry.originalFilename) || 'png';
      const newFilename = `${formattedTimestamp}_${promptId}.${extension}`;

      // Use Chrome's download API to suggest new filename
      await chrome.downloads.setShelfEnabled(false);

      // Try to rename using the downloads API
      try {
        await chrome.downloads.setShelfEnabled(true);
        // Note: Chrome doesn't allow direct renaming, but we can suggest it
        this.log(`Suggested filename: ${newFilename}`);
        queueEntry.suggestedFilename = newFilename;
      } catch (error) {
        this.log(`Could not suggest filename: ${error.message}`);
      }

    } catch (error) {
      this.log(`Error suggesting filename: ${error.message}`);
    }
  }

  handleDownloadChanged(downloadDelta) {
    if (downloadDelta.state && downloadDelta.state.current === 'complete') {
      this.handleDownloadComplete(downloadDelta.id);
    }
  }

  async handleDownloadComplete(downloadId) {
    this.log(`Download completed: ${downloadId}`);

    // Find the corresponding queue entry
    const queueEntry = Array.from(this.downloadQueue.values()).find(entry =>
      entry.downloadId === downloadId
    );

    if (!queueEntry) {
      return;
    }

    try {
      // Get download info
      const downloads = await chrome.downloads.search({ id: downloadId });
      if (downloads.length === 0) {
        return;
      }

      const download = downloads[0];
      const newFilename = queueEntry.suggestedFilename || `${Date.now()}.png`;

      // Try to rename the file using Chrome downloads API
      try {
        // Get the directory path
        const originalPath = download.filename;
        const directory = originalPath.substring(0, originalPath.lastIndexOf('/') + 1);
        const newPath = directory + newFilename;

        // Attempt to rename using Chrome's downloads API
        await chrome.downloads.setShelfEnabled(false);

        // Use the erase and re-download approach (not ideal but works)
        this.log(`Attempting to rename: ${originalPath} -> ${newFilename}`);

        // For now, we'll just log the intended filename
        // Chrome extensions have limited file system access

      } catch (renameError) {
        this.log(`Could not auto-rename file: ${renameError.message}`);
      }

      this.log(`Download completed with suggested name: ${newFilename}`);
      this.log(`Actual file location: ${download.filename}`);
      this.log(`NOTE: Please manually rename the file to: ${newFilename}`);

      // Update queue entry
      queueEntry.status = 'completed';
      queueEntry.finalFilename = newFilename;
      queueEntry.downloadPath = download.filename;

      // Store download history
      await this.storeDownloadHistory(queueEntry);

      // Broadcast completion with rename instruction
      this.broadcastDownloadComplete(queueEntry);

    } catch (error) {
      this.log(`Error handling download completion: ${error.message}`);
    }
  }

  async storeDownloadHistory(queueEntry) {
    try {
      // Get existing download history
      const result = await chrome.storage.local.get(['downloadHistory']);
      const history = result.downloadHistory || [];

      // Add new entry
      history.push({
        id: queueEntry.promptData.id,
        prompt: queueEntry.promptData.visual_prompt,
        timestamp: queueEntry.timestamp,
        filename: queueEntry.finalFilename,
        downloadPath: queueEntry.downloadPath,
        status: 'completed'
      });

      // Keep only last 100 downloads
      if (history.length > 100) {
        history.splice(0, history.length - 100);
      }

      // Save back to storage
      await chrome.storage.local.set({ downloadHistory: history });

    } catch (error) {
      this.log(`Error storing download history: ${error.message}`);
    }
  }

  formatTimestamp(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}${month}${day}_${hours}${minutes}${seconds}`;
  }

  getFileExtension(filename) {
    const lastDot = filename.lastIndexOf('.');
    return lastDot > 0 ? filename.substring(lastDot + 1) : null;
  }

  handleDebugLog(message, level, timestamp) {
    // Store debug logs for analysis
    this.log(`[CONTENT] ${message}`);

    // Optionally store in chrome.storage for later analysis
    if (level === 'ERROR' || level === 'WARNING') {
      this.storeDebugLog(message, level, timestamp);
    }
  }

  async storeDebugLog(message, level, timestamp) {
    try {
      const result = await chrome.storage.local.get(['debugLogs']);
      const logs = result.debugLogs || [];

      logs.push({
        message,
        level,
        timestamp,
        sessionTime: new Date().toISOString()
      });

      // Keep only last 100 debug logs
      if (logs.length > 100) {
        logs.splice(0, logs.length - 100);
      }

      await chrome.storage.local.set({ debugLogs: logs });
    } catch (error) {
      this.log(`Error storing debug log: ${error.message}`);
    }
  }

  broadcastDownloadComplete(queueEntry) {
    chrome.runtime.sendMessage({
      action: 'downloadComplete',
      promptData: queueEntry.promptData,
      filename: queueEntry.finalFilename,
      timestamp: queueEntry.timestamp
    }).catch(() => {
      // Popup might not be open, ignore error
    });
  }
}

// Initialize background manager
new BackgroundManager();
