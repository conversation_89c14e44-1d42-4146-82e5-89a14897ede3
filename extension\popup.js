// Gemini Auto Image Generator - Popup Script
// Handles UI interactions and communication with background script

class PopupManager {
  constructor() {
    this.prompts = [];
    this.isRunning = false;
    this.currentStatus = 'idle';
    
    this.initializeElements();
    this.setupEventListeners();
    this.setupMessageListener();
    this.updateStatus();
    this.log('Extension popup initialized');
  }

  initializeElements() {
    // File upload elements
    this.fileUploadArea = document.getElementById('fileUploadArea');
    this.fileInput = document.getElementById('fileInput');
    this.fileInfo = document.getElementById('fileInfo');
    this.fileName = document.getElementById('fileName');
    this.fileDetails = document.getElementById('fileDetails');

    // Status elements
    this.statusIndicator = document.getElementById('statusIndicator');
    this.statusText = document.getElementById('statusText');
    this.statusMessage = document.getElementById('statusMessage');

    // Progress elements
    this.progressSection = document.getElementById('progressSection');
    this.progressFill = document.getElementById('progressFill');
    this.progressText = document.getElementById('progressText');
    this.progressPercent = document.getElementById('progressPercent');
    this.currentPrompt = document.getElementById('currentPrompt');
    this.currentPromptText = document.getElementById('currentPromptText');

    // Control buttons
    this.startBtn = document.getElementById('startBtn');
    this.pauseBtn = document.getElementById('pauseBtn');
    this.stopBtn = document.getElementById('stopBtn');

    // Log container
    this.logContainer = document.getElementById('logContainer');
  }

  setupEventListeners() {
    // File upload events
    this.fileUploadArea.addEventListener('click', () => {
      this.fileInput.click();
    });

    this.fileUploadArea.addEventListener('dragover', (e) => {
      e.preventDefault();
      this.fileUploadArea.classList.add('dragover');
    });

    this.fileUploadArea.addEventListener('dragleave', () => {
      this.fileUploadArea.classList.remove('dragover');
    });

    this.fileUploadArea.addEventListener('drop', (e) => {
      e.preventDefault();
      this.fileUploadArea.classList.remove('dragover');
      const files = e.dataTransfer.files;
      if (files.length > 0) {
        this.handleFileSelect(files[0]);
      }
    });

    this.fileInput.addEventListener('change', (e) => {
      if (e.target.files.length > 0) {
        this.handleFileSelect(e.target.files[0]);
      }
    });

    // Control button events
    this.startBtn.addEventListener('click', () => this.startAutomation());
    this.pauseBtn.addEventListener('click', () => this.pauseAutomation());
    this.stopBtn.addEventListener('click', () => this.stopAutomation());

    // Footer link events
    document.getElementById('helpLink').addEventListener('click', (e) => {
      e.preventDefault();
      this.showHelp();
    });

    document.getElementById('settingsLink').addEventListener('click', (e) => {
      e.preventDefault();
      this.showSettings();
    });
  }

  setupMessageListener() {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true;
    });
  }

  handleMessage(request, sender, sendResponse) {
    switch (request.action) {
      case 'statusBroadcast':
        this.updateStatusFromBackground(request.status);
        break;
      case 'downloadComplete':
        this.handleDownloadComplete(request);
        break;
    }
    sendResponse({ success: true });
  }

  async handleFileSelect(file) {
    if (!file.name.endsWith('.json')) {
      this.showError('Please select a JSON file');
      return;
    }

    try {
      const fileContent = await this.readFile(file);
      this.log(`Loading file: ${file.name}`);
      
      const response = await chrome.runtime.sendMessage({
        action: 'processJsonFile',
        fileContent: fileContent
      });

      if (response.success) {
        this.prompts = response.prompts;
        this.displayFileInfo(file, this.prompts.length);
        this.startBtn.disabled = false;
        this.log(`Loaded ${this.prompts.length} prompts from ${file.name}`);
      } else {
        this.showError(response.error || 'Failed to process JSON file');
      }
    } catch (error) {
      this.showError(`Error reading file: ${error.message}`);
    }
  }

  readFile(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target.result);
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  }

  displayFileInfo(file, promptCount) {
    this.fileName.textContent = file.name;
    this.fileDetails.textContent = `${promptCount} prompts • ${this.formatFileSize(file.size)}`;
    this.fileInfo.style.display = 'block';
  }

  formatFileSize(bytes) {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  }

  async startAutomation() {
    if (this.prompts.length === 0) {
      this.showError('No prompts loaded. Please upload a JSON file first.');
      return;
    }

    try {
      this.log('Starting automation...');
      
      const response = await chrome.runtime.sendMessage({
        action: 'startAutomation',
        prompts: this.prompts
      });

      if (response.success) {
        this.isRunning = true;
        this.updateControlButtons();
        this.progressSection.style.display = 'block';
      } else {
        this.showError(response.error || 'Failed to start automation');
      }
    } catch (error) {
      this.showError(`Error starting automation: ${error.message}`);
    }
  }

  async pauseAutomation() {
    // Note: Pause functionality would need to be implemented in content script
    this.log('Pause functionality not yet implemented');
  }

  async stopAutomation() {
    try {
      this.log('Stopping automation...');
      
      const response = await chrome.runtime.sendMessage({
        action: 'stopAutomation'
      });

      if (response.success) {
        this.isRunning = false;
        this.updateControlButtons();
      } else {
        this.showError(response.error || 'Failed to stop automation');
      }
    } catch (error) {
      this.showError(`Error stopping automation: ${error.message}`);
    }
  }

  updateStatusFromBackground(status) {
    this.currentStatus = status.status;
    this.isRunning = status.isRunning;
    
    // Update status display
    this.statusText.textContent = this.getStatusDisplayText(status.status);
    this.statusMessage.textContent = status.message;
    
    // Update status indicator
    const statusDot = this.statusIndicator.querySelector('.status-dot');
    statusDot.className = `status-dot ${status.status}`;
    
    // Update progress
    if (status.totalPrompts > 0) {
      this.updateProgress(status.currentIndex, status.totalPrompts);
      
      if (status.data && status.data.prompt) {
        this.currentPromptText.textContent = status.data.prompt.substring(0, 100) + '...';
        this.currentPrompt.style.display = 'block';
      }
    }
    
    this.updateControlButtons();
  }

  updateProgress(current, total) {
    const percent = total > 0 ? Math.round((current / total) * 100) : 0;
    this.progressFill.style.width = `${percent}%`;
    this.progressText.textContent = `${current} / ${total}`;
    this.progressPercent.textContent = `${percent}%`;
  }

  getStatusDisplayText(status) {
    const statusMap = {
      'idle': 'Ready',
      'starting': 'Starting...',
      'running': 'Running',
      'processing': 'Processing',
      'completed': 'Completed',
      'stopped': 'Stopped',
      'error': 'Error'
    };
    return statusMap[status] || status;
  }

  updateControlButtons() {
    this.startBtn.disabled = this.isRunning || this.prompts.length === 0;
    this.pauseBtn.disabled = !this.isRunning;
    this.stopBtn.disabled = !this.isRunning;
  }

  handleDownloadComplete(data) {
    this.log(`Downloaded: ${data.filename}`, 'success');
  }

  showError(message) {
    this.log(message, 'error');
    this.statusText.textContent = 'Error';
    this.statusMessage.textContent = message;
    
    const statusDot = this.statusIndicator.querySelector('.status-dot');
    statusDot.className = 'status-dot error';
  }

  updateStatus() {
    // Get current status from background script
    chrome.runtime.sendMessage({ action: 'getStatus' })
      .then(response => {
        if (response.success) {
          this.updateStatusFromBackground(response.status);
        }
      })
      .catch(() => {
        // Background script might not be ready yet
      });
  }

  log(message, type = 'info') {
    const time = new Date().toLocaleTimeString('en-US', { hour12: false });
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry ${type}`;
    logEntry.innerHTML = `
      <span class="log-time">${time}</span>
      <span class="log-message">${message}</span>
    `;
    
    this.logContainer.appendChild(logEntry);
    this.logContainer.scrollTop = this.logContainer.scrollHeight;
    
    // Keep only last 50 log entries
    while (this.logContainer.children.length > 50) {
      this.logContainer.removeChild(this.logContainer.firstChild);
    }
  }

  showHelp() {
    alert(`Gemini Auto Image Generator Help:

1. Upload a JSON file with visual_prompt fields
2. Make sure you're on gemini.google.com
3. Click Start Automation to begin
4. The extension will automatically:
   - Type each prompt
   - Generate images
   - Download them with timestamps

JSON Format:
{
  "id": "unique_id",
  "visual_prompt": "your image prompt here"
}`);
  }

  showSettings() {
    alert('Settings panel coming soon!');
  }
}

// Initialize popup when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new PopupManager();
  });
} else {
  new PopupManager();
}
