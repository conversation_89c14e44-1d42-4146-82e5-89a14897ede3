# Installation Guide - Gemini Auto Image Generator

## Quick Start

### 1. Download the Extension
- Download all files from the `extension/` folder
- Keep the folder structure intact

### 2. Add Icons (Optional)
Create or download icon files and place them in the `icons/` folder:
- `icon16.png` (16x16 pixels)
- `icon32.png` (32x32 pixels)
- `icon48.png` (48x48 pixels)
- `icon128.png` (128x128 pixels)

You can use any square PNG images for now, or create custom icons later.

### 3. Load in Chrome
1. Open Chrome browser
2. Go to `chrome://extensions/`
3. Enable "Developer mode" (toggle in top right)
4. Click "Load unpacked"
5. Select the `extension` folder
6. The extension should now appear in your extensions list

### 4. Pin the Extension (Recommended)
1. Click the puzzle piece icon in Chrome toolbar
2. Find "Gemini Auto Image Generator"
3. Click the pin icon to keep it visible

## First Use

### 1. Open Gemini
- Navigate to [gemini.google.com](https://gemini.google.com)
- Make sure you're logged in
- Go to a page that supports image generation

### 2. Test with Sample File
- Use the included `sample-prompts.json` file
- Click the extension icon
- Upload the sample file
- Click "Start Automation"

### 3. Create Your Own JSON
Follow this format:
```json
[
  {
    "id": "my_prompt_1",
    "visual_prompt": "A beautiful sunset over mountains"
  },
  {
    "id": "my_prompt_2", 
    "visual_prompt": "A futuristic city with flying cars"
  }
]
```

## Troubleshooting Installation

### Extension Won't Load
- Check that all files are in the correct folder structure
- Ensure `manifest.json` is in the root of the extension folder
- Try refreshing the extensions page

### Missing Icons Error
- Create placeholder icon files or ignore the warning
- The extension will work without custom icons

### Permission Errors
- Make sure Chrome is up to date
- Try disabling other extensions temporarily
- Restart Chrome and try again

## File Structure Check

Your extension folder should look like this:
```
extension/
├── manifest.json
├── popup.html
├── popup.css
├── popup.js
├── content.js
├── background.js
├── sample-prompts.json
├── README.md
├── INSTALLATION.md
└── icons/
    └── README.md
```

## Next Steps

1. **Test the Extension**: Use the sample JSON file first
2. **Create Your Prompts**: Make your own JSON file with image prompts
3. **Customize Settings**: Modify timeouts and retry counts if needed
4. **Report Issues**: Check the console for any error messages

## Security Note

This extension only works on gemini.google.com and doesn't collect or transmit any personal data. All processing happens locally in your browser.
