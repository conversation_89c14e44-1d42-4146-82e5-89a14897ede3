// Auto Image Generator - Content Script
// Handles DOM interactions with the current page

class ImageAutomator {
  constructor() {
    this.isRunning = false;
    this.currentPromptIndex = 0;
    this.prompts = [];
    this.maxRetries = 3;
    this.retryDelay = 2000;
    this.downloadTimeout = 60000; // 60 seconds timeout for download button

    this.setupMessageListener();
    this.log('Image Automator initialized');
  }

  log(message, level = 'INFO') {
    const timestamp = new Date().toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      fractionalSecondDigits: 3
    });

    const levelEmojis = {
      'INFO': 'ℹ️',
      'SUCCESS': '✅',
      'WARNING': '⚠️',
      'ERROR': '❌',
      'DEBUG': '🔍',
      'TIMING': '⏱️',
      'STATE': '📊',
      'DOM': '🌐',
      'CLICK': '👆',
      'TYPE': '⌨️',
      'WAIT': '⏳'
    };

    const emoji = levelEmojis[level] || 'ℹ️';
    const formattedMessage = `[${timestamp}] ${emoji} [${level}] ${message}`;

    console.log(`[Image Automator] ${formattedMessage}`);

    // Also send to background for popup display
    try {
      chrome.runtime.sendMessage({
        action: 'debugLog',
        message: formattedMessage,
        level: level,
        timestamp: timestamp
      });
    } catch (e) {
      // Ignore if popup is closed
    }
  }

  logCheckpoint(step, promptIndex, totalPrompts, details = '') {
    const progress = `[${promptIndex + 1}/${totalPrompts}]`;
    this.log(`🎯 CHECKPOINT ${step} ${progress} ${details}`, 'STATE');
  }

  logTiming(operation, startTime) {
    const duration = Date.now() - startTime;
    this.log(`⏱️ ${operation} took ${duration}ms`, 'TIMING');
  }

  logDOMState(context) {
    const textareas = document.querySelectorAll('textarea').length;
    const buttons = document.querySelectorAll('button').length;
    const matIcons = document.querySelectorAll('mat-icon').length;
    const downloadIcons = document.querySelectorAll('mat-icon').length;

    this.log(`🌐 DOM STATE [${context}]: ${textareas} textareas, ${buttons} buttons, ${matIcons} mat-icons`, 'DOM');
  }

  setupMessageListener() {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // Keep message channel open for async response
    });
  }

  async handleMessage(request, sender, sendResponse) {
    try {
      switch (request.action) {
        case 'startAutomation':
          await this.startAutomation(request.prompts, request.saveMethod);
          sendResponse({ success: true });
          break;
        case 'stopAutomation':
          this.stopAutomation();
          sendResponse({ success: true });
          break;
        case 'getStatus':
          sendResponse({ 
            isRunning: this.isRunning,
            currentIndex: this.currentPromptIndex,
            totalPrompts: this.prompts.length
          });
          break;
        default:
          sendResponse({ success: false, error: 'Unknown action' });
      }
    } catch (error) {
      this.log(`Error handling message: ${error.message}`);
      sendResponse({ success: false, error: error.message });
    }
  }

  async startAutomation(prompts, saveMethod = 'download') {
    if (this.isRunning) {
      throw new Error('Automation is already running');
    }

    this.prompts = prompts;
    this.currentPromptIndex = 0;
    this.isRunning = true;
    this.saveMethod = saveMethod;

    this.log(`🚀 STARTING AUTOMATION with ${prompts.length} prompts (${saveMethod} mode)`, 'SUCCESS');
    this.logDOMState('AUTOMATION_START');
    this.sendStatusUpdate('started', `Automation started (${saveMethod} mode)`);

    const automationStartTime = Date.now();

    try {
      for (let i = 0; i < prompts.length; i++) {
        const promptStartTime = Date.now();

        if (!this.isRunning) {
          this.log('🛑 Automation stopped by user', 'WARNING');
          break;
        }

        this.currentPromptIndex = i;
        const prompt = prompts[i];

        this.log(`\n${'='.repeat(80)}`, 'STATE');
        this.log(`🎯 STARTING PROMPT ${i + 1}/${prompts.length}`, 'STATE');
        this.log(`📝 Prompt ID: ${prompt.id}`, 'DEBUG');
        this.log(`📝 Prompt Text: "${prompt.visual_prompt.substring(0, 100)}${prompt.visual_prompt.length > 100 ? '...' : ''}"`, 'DEBUG');
        this.log(`${'='.repeat(80)}`, 'STATE');

        this.logCheckpoint('PROMPT_START', i, prompts.length, `ID: ${prompt.id}`);
        this.logDOMState(`PROMPT_${i + 1}_START`);

        this.sendStatusUpdate('processing', `Processing prompt ${i + 1}/${prompts.length}`, {
          currentIndex: i,
          totalPrompts: prompts.length,
          prompt: prompt.visual_prompt
        });

        try {
          await this.processPrompt(prompt, i, prompts.length);

          this.logTiming(`Prompt ${i + 1}`, promptStartTime);
          this.log(`✅ COMPLETED prompt ${i + 1}/${prompts.length}`, 'SUCCESS');
          this.logCheckpoint('PROMPT_COMPLETE', i, prompts.length, `Success`);

        } catch (promptError) {
          this.log(`❌ FAILED prompt ${i + 1}/${prompts.length}: ${promptError.message}`, 'ERROR');
          this.logCheckpoint('PROMPT_FAILED', i, prompts.length, `Error: ${promptError.message}`);
          throw promptError;
        }

        // Inter-prompt delay and state check
        if (i < prompts.length - 1) {
          this.log(`⏳ INTER-PROMPT DELAY: Waiting 3s before prompt ${i + 2}/${prompts.length}`, 'WAIT');
          this.logDOMState(`BEFORE_DELAY_${i + 1}`);

          await this.delay(3000);

          this.logDOMState(`AFTER_DELAY_${i + 1}`);
          this.log(`✅ Ready for next prompt ${i + 2}/${prompts.length}`, 'SUCCESS');
        }
      }

      this.logTiming('Total automation', automationStartTime);
      this.sendStatusUpdate('completed', 'All prompts processed successfully');
      this.log(`🎉 AUTOMATION COMPLETED! Successfully processed ${prompts.length} prompts.`, 'SUCCESS');

    } catch (error) {
      this.logTiming('Failed automation', automationStartTime);
      this.log(`💥 AUTOMATION FAILED: ${error.message}`, 'ERROR');
      this.log(`📍 Failed at prompt ${this.currentPromptIndex + 1}/${prompts.length}`, 'ERROR');
      this.logDOMState('AUTOMATION_FAILED');
      this.sendStatusUpdate('error', error.message);
    } finally {
      this.isRunning = false;
      this.log(`🏁 Automation session ended`, 'STATE');
    }
  }

  stopAutomation() {
    this.isRunning = false;
    this.log('Automation stopped by user');
    this.sendStatusUpdate('stopped', 'Automation stopped by user');
  }

  async processPrompt(promptData, promptIndex, totalPrompts) {
    const prompt = promptData.visual_prompt;
    const promptId = promptData.id;

    this.log(`\n📝 PROCESSING PROMPT [${promptIndex + 1}/${totalPrompts}]`, 'STATE');
    this.log(`🆔 Prompt ID: ${promptId}`, 'DEBUG');
    this.log(`📄 Prompt: "${prompt.substring(0, 100)}${prompt.length > 100 ? '...' : ''}"`, 'DEBUG');

    let retryCount = 0;
    const promptStartTime = Date.now();

    while (retryCount < this.maxRetries) {
      const attemptStartTime = Date.now();

      try {
        this.log(`\n🔄 ATTEMPT ${retryCount + 1}/${this.maxRetries} for prompt ${promptIndex + 1}`, 'STATE');
        this.logDOMState(`ATTEMPT_${retryCount + 1}_START`);

        // Step 0: Ensure page is ready
        this.log('0️⃣ STEP: Ensuring page readiness...', 'DEBUG');
        const readyStartTime = Date.now();
        await this.ensurePageReady(promptIndex, retryCount);
        this.logTiming('Page readiness check', readyStartTime);

        // Step 1: Find and clear textarea
        this.log('1️⃣ STEP: Finding and clearing textarea...', 'DEBUG');
        const textareaStartTime = Date.now();
        const textarea = await this.findTextareaWithRetry(promptIndex, retryCount);
        this.logTiming('Textarea finding', textareaStartTime);

        const clearStartTime = Date.now();
        await this.clearTextarea(textarea, promptIndex, retryCount);
        this.logTiming('Textarea clearing', clearStartTime);

        // Step 2: Type the prompt
        this.log('2️⃣ STEP: Typing prompt...', 'DEBUG');
        const typeStartTime = Date.now();
        await this.typePrompt(textarea, prompt, promptIndex, retryCount);
        this.logTiming('Prompt typing', typeStartTime);

        // Step 3: Click run button
        this.log('3️⃣ STEP: Clicking run button...', 'DEBUG');
        const runStartTime = Date.now();
        await this.clickRunButtonWithRetry(promptIndex, retryCount);
        this.logTiming('Run button click', runStartTime);

        // Step 4: Wait for save button and click it
        this.log(`4️⃣ STEP: Waiting for generation and ${this.saveMethod}...`, 'DEBUG');
        const saveStartTime = Date.now();
        await this.waitAndSaveWithRetry(promptData, promptIndex, retryCount);
        this.logTiming(`${this.saveMethod} process`, saveStartTime);

        // Step 5: Clear textarea for next prompt
        this.log('5️⃣ STEP: Clearing textarea for next prompt...', 'DEBUG');
        const finalClearStartTime = Date.now();
        await this.clearTextarea(textarea, promptIndex, retryCount, 'FINAL');
        this.logTiming('Final textarea clear', finalClearStartTime);

        this.logTiming(`Successful attempt ${retryCount + 1}`, attemptStartTime);
        this.log(`✅ PROMPT PROCESSING COMPLETED SUCCESSFULLY! [${promptIndex + 1}/${totalPrompts}]`, 'SUCCESS');
        this.logDOMState(`PROMPT_${promptIndex + 1}_SUCCESS`);

        // Success - break out of retry loop
        break;

      } catch (error) {
        retryCount++;
        this.logTiming(`Failed attempt ${retryCount}`, attemptStartTime);
        this.log(`❌ ATTEMPT ${retryCount} FAILED for prompt ${promptIndex + 1}: ${error.message}`, 'ERROR');
        this.log(`📍 Error stack: ${error.stack}`, 'DEBUG');
        this.logDOMState(`ATTEMPT_${retryCount}_FAILED`);

        if (retryCount >= this.maxRetries) {
          this.logTiming(`All attempts failed for prompt ${promptIndex + 1}`, promptStartTime);
          this.log(`💥 ALL ATTEMPTS EXHAUSTED for prompt ${promptIndex + 1}/${totalPrompts}`, 'ERROR');
          this.sendStatusUpdate('error', `Failed after ${this.maxRetries} attempts: ${error.message}`);
          throw error;
        }

        this.log(`⏳ RETRYING in ${this.retryDelay/1000}s... (attempt ${retryCount + 1}/${this.maxRetries})`, 'WARNING');
        this.sendStatusUpdate('retrying', `Retrying in ${this.retryDelay/1000}s... (${retryCount}/${this.maxRetries})`);

        const delayStartTime = Date.now();
        await this.delay(this.retryDelay);
        this.logTiming('Retry delay', delayStartTime);

        // Try to recover by refreshing the page state
        this.log('🔧 ATTEMPTING RECOVERY...', 'WARNING');
        const recoveryStartTime = Date.now();
        await this.attemptRecovery(promptIndex, retryCount);
        this.logTiming('Recovery attempt', recoveryStartTime);
      }
    }

    this.logTiming(`Complete prompt ${promptIndex + 1} processing`, promptStartTime);
  }

  async findTextareaWithRetry(promptIndex = 0, retryCount = 0) {
    this.log(`🔍 FINDING TEXTAREA [P${promptIndex + 1}:A${retryCount + 1}]`, 'DEBUG');

    for (let attempt = 0; attempt < 3; attempt++) {
      const attemptStartTime = Date.now();

      try {
        this.log(`🔍 Textarea search attempt ${attempt + 1}/3`, 'DEBUG');
        this.logDOMState(`TEXTAREA_SEARCH_${attempt + 1}`);

        const textarea = await this.findTextarea(promptIndex, retryCount, attempt);
        if (textarea) {
          this.logTiming(`Textarea found on attempt ${attempt + 1}`, attemptStartTime);
          this.log(`✅ TEXTAREA FOUND on attempt ${attempt + 1}`, 'SUCCESS');
          return textarea;
        }

      } catch (error) {
        this.logTiming(`Textarea search attempt ${attempt + 1} failed`, attemptStartTime);
        this.log(`❌ Textarea search attempt ${attempt + 1} failed: ${error.message}`, 'ERROR');
        if (attempt < 2) {
          this.log(`⏳ Waiting 1s before next textarea search attempt...`, 'WAIT');
          await this.delay(1000);
        }
      }
    }

    this.log(`💥 TEXTAREA NOT FOUND after 3 attempts [P${promptIndex + 1}:A${retryCount + 1}]`, 'ERROR');
    throw new Error('Could not find textarea after multiple attempts');
  }

  async clickRunButtonWithRetry() {
    for (let attempt = 0; attempt < 3; attempt++) {
      try {
        await this.clickRunButton();
        return;
      } catch (error) {
        this.log(`Run button click attempt ${attempt + 1} failed: ${error.message}`);
        if (attempt < 2) await this.delay(1000);
      }
    }
    throw new Error('Could not click run button after multiple attempts');
  }

  async waitAndSaveWithRetry(promptData, promptIndex, retryCount) {
    const context = `[P${promptIndex + 1}:A${retryCount + 1}]`;

    for (let attempt = 0; attempt < 2; attempt++) {
      try {
        this.log(`💾 ${this.saveMethod.toUpperCase()} attempt ${attempt + 1}/2 ${context}`, 'DEBUG');

        if (this.saveMethod === 'download') {
          await this.waitAndDownload(promptData, promptIndex, retryCount);
        } else if (this.saveMethod === 'drive') {
          await this.waitAndSaveToDrive(promptData, promptIndex, retryCount);
        }

        this.log(`✅ ${this.saveMethod.toUpperCase()} completed successfully ${context}`, 'SUCCESS');
        return;

      } catch (error) {
        this.log(`❌ ${this.saveMethod.toUpperCase()} attempt ${attempt + 1} failed: ${error.message} ${context}`, 'ERROR');
        if (attempt < 1) {
          this.log(`⏳ Retrying ${this.saveMethod} detection... ${context}`, 'WARNING');
          await this.delay(5000); // Wait longer before retry
        }
      }
    }
    throw new Error(`Could not complete ${this.saveMethod} after multiple attempts`);
  }

  async attemptRecovery() {
    this.log('🔧 Attempting recovery...');

    try {
      // Clear any existing content
      const textarea = document.querySelector('textarea');
      if (textarea) {
        textarea.value = '';
        textarea.dispatchEvent(new Event('input', { bubbles: true }));
        this.log('✓ Cleared textarea content');
      }

      // Check if there are any error messages or loading states
      const errorElements = document.querySelectorAll('[class*="error"], [class*="Error"]');
      if (errorElements.length > 0) {
        this.log(`⚠️ Found ${errorElements.length} potential error elements on page`);
      }

      // Wait for page to stabilize
      this.log('⏳ Waiting for page to stabilize...');
      await this.delay(3000);

      this.log('✓ Recovery attempt completed');

    } catch (error) {
      this.log(`❌ Recovery attempt failed: ${error.message}`);
    }
  }

  async ensurePageReady(promptIndex = 0, retryCount = 0) {
    const context = `[P${promptIndex + 1}:A${retryCount + 1}]`;
    const startTime = Date.now();

    this.log(`🔍 ENSURING PAGE READY ${context}`, 'STATE');
    this.logDOMState(`PAGE_READY_CHECK_START_${context}`);

    // Wait for any loading indicators to disappear
    const maxWait = 15000; // 15 seconds
    let checkCount = 0;

    while (Date.now() - startTime < maxWait) {
      checkCount++;
      const elapsed = Date.now() - startTime;

      // Check for various loading indicators
      const loadingSelectors = [
        '[class*="loading"]',
        '[class*="Loading"]',
        '[aria-label*="loading" i]',
        '[class*="spinner"]',
        '[class*="Spinner"]',
        '.loading',
        '.spinner'
      ];

      let totalLoadingElements = 0;
      const loadingDetails = [];

      loadingSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        if (elements.length > 0) {
          totalLoadingElements += elements.length;
          loadingDetails.push(`${selector}: ${elements.length}`);
        }
      });

      // Check for disabled states that might indicate processing
      const disabledButtons = document.querySelectorAll('button:disabled').length;
      const textareas = document.querySelectorAll('textarea');
      const enabledTextareas = Array.from(textareas).filter(ta => !ta.disabled && ta.offsetParent !== null).length;

      this.log(`📊 PAGE STATE CHECK ${checkCount} ${context}:`, 'DEBUG');
      this.log(`   - Loading elements: ${totalLoadingElements} (${loadingDetails.join(', ')})`, 'DEBUG');
      this.log(`   - Disabled buttons: ${disabledButtons}`, 'DEBUG');
      this.log(`   - Enabled visible textareas: ${enabledTextareas}/${textareas.length}`, 'DEBUG');
      this.log(`   - Document ready state: ${document.readyState}`, 'DEBUG');

      // Page is ready if no loading indicators and we have accessible textareas
      if (totalLoadingElements === 0 && enabledTextareas > 0) {
        this.logTiming(`Page readiness check ${context}`, startTime);
        this.log(`✅ PAGE READY after ${checkCount} checks (${elapsed}ms) ${context}`, 'SUCCESS');
        this.logDOMState(`PAGE_READY_SUCCESS_${context}`);
        return;
      }

      // Log detailed status every 5 seconds
      if (elapsed > 0 && elapsed % 5000 < 1000) {
        this.log(`⏳ Page not ready yet (${Math.round(elapsed/1000)}s): ${totalLoadingElements} loading, ${enabledTextareas} textareas ${context}`, 'WAIT');
      }

      await this.delay(1000);
    }

    this.logTiming(`Page readiness timeout ${context}`, startTime);
    this.log(`⚠️ PAGE READINESS TIMEOUT after ${checkCount} checks - proceeding anyway ${context}`, 'WARNING');
    this.logDOMState(`PAGE_READY_TIMEOUT_${context}`);

    // Final state log
    const finalTextareas = document.querySelectorAll('textarea').length;
    const finalButtons = document.querySelectorAll('button').length;
    this.log(`📊 FINAL PAGE STATE ${context}: ${finalTextareas} textareas, ${finalButtons} buttons`, 'STATE');
  }

  async findTextarea(promptIndex = 0, retryCount = 0, attempt = 0) {
    const context = `[P${promptIndex + 1}:A${retryCount + 1}:S${attempt + 1}]`;

    const selectors = [
      'textarea[aria-label="Enter a prompt to generate an image"]',
      'textarea[formcontrolname="prompt"]',
      'textarea.textarea',
      'textarea[placeholder="Describe your image"]',
      'textarea' // Fallback to any textarea
    ];

    this.log(`🔍 Searching for textarea with ${selectors.length} selectors ${context}`, 'DEBUG');

    for (let i = 0; i < selectors.length; i++) {
      const selector = selectors[i];
      const selectorStartTime = Date.now();

      this.log(`🔍 Trying selector ${i + 1}/${selectors.length}: "${selector}" ${context}`, 'DEBUG');

      const element = await this.waitForElement(selector, 2000, context);

      if (element) {
        this.logTiming(`Selector ${i + 1} success`, selectorStartTime);
        this.log(`✅ TEXTAREA FOUND with selector: "${selector}" ${context}`, 'SUCCESS');
        this.log(`📏 Textarea properties: visible=${element.offsetParent !== null}, enabled=${!element.disabled}, value="${element.value}"`, 'DEBUG');
        return element;
      } else {
        this.logTiming(`Selector ${i + 1} failed`, selectorStartTime);
        this.log(`❌ Selector failed: "${selector}" ${context}`, 'DEBUG');
      }
    }

    // Final check - log all textareas on page
    const allTextareas = document.querySelectorAll('textarea');
    this.log(`🔍 FALLBACK: Found ${allTextareas.length} total textareas on page ${context}`, 'DEBUG');

    allTextareas.forEach((ta, index) => {
      this.log(`📝 Textarea ${index + 1}: id="${ta.id}", class="${ta.className}", placeholder="${ta.placeholder}", visible=${ta.offsetParent !== null}`, 'DEBUG');
    });

    throw new Error(`Could not find prompt textarea with any selector ${context}`);
  }

  async clearTextarea(textarea, promptIndex = 0, retryCount = 0, phase = 'INITIAL') {
    const context = `[P${promptIndex + 1}:A${retryCount + 1}:${phase}]`;
    const startTime = Date.now();

    this.log(`🧹 CLEARING TEXTAREA ${context}`, 'DEBUG');
    this.log(`📝 Textarea before clear: value="${textarea.value}", focused=${document.activeElement === textarea}`, 'DEBUG');

    try {
      // Focus the textarea
      this.log(`👆 Focusing textarea ${context}`, 'DEBUG');
      textarea.focus();
      await this.delay(100);

      // Select all content
      this.log(`📋 Selecting all content ${context}`, 'DEBUG');
      textarea.select();
      await this.delay(100);

      // Clear the value
      this.log(`🗑️ Clearing value ${context}`, 'DEBUG');
      const oldValue = textarea.value;
      textarea.value = '';

      // Trigger events to ensure framework detects the change
      this.log(`📡 Triggering input events ${context}`, 'DEBUG');
      textarea.dispatchEvent(new Event('input', { bubbles: true }));
      textarea.dispatchEvent(new Event('change', { bubbles: true }));
      textarea.dispatchEvent(new Event('keyup', { bubbles: true }));

      await this.delay(500);

      // Verify clearing worked
      const newValue = textarea.value;
      this.log(`📝 Textarea after clear: old="${oldValue}", new="${newValue}" ${context}`, 'DEBUG');

      if (newValue !== '') {
        this.log(`⚠️ WARNING: Textarea not fully cleared! Still contains: "${newValue}" ${context}`, 'WARNING');
      } else {
        this.log(`✅ TEXTAREA CLEARED SUCCESSFULLY ${context}`, 'SUCCESS');
      }

      this.logTiming(`Textarea clearing ${context}`, startTime);

    } catch (error) {
      this.log(`❌ ERROR clearing textarea ${context}: ${error.message}`, 'ERROR');
      throw error;
    }
  }

  async typePrompt(textarea, prompt, promptIndex = 0, retryCount = 0) {
    const context = `[P${promptIndex + 1}:A${retryCount + 1}]`;
    const startTime = Date.now();

    this.log(`⌨️ TYPING PROMPT ${context}`, 'TYPE');
    this.log(`📝 Prompt length: ${prompt.length} characters`, 'DEBUG');
    this.log(`📝 Prompt preview: "${prompt.substring(0, 100)}${prompt.length > 100 ? '...' : ''}"`, 'DEBUG');

    try {
      // Ensure textarea is focused
      this.log(`👆 Focusing textarea for typing ${context}`, 'DEBUG');
      textarea.focus();
      await this.delay(200);

      if (document.activeElement !== textarea) {
        this.log(`⚠️ WARNING: Textarea not focused after focus attempt ${context}`, 'WARNING');
      }

      // Type character by character to simulate human typing
      this.log(`⌨️ Starting character-by-character typing ${context}`, 'DEBUG');
      let typedChars = 0;

      for (let i = 0; i < prompt.length; i++) {
        if (!this.isRunning) {
          this.log(`🛑 Typing interrupted by stop signal at character ${i} ${context}`, 'WARNING');
          break;
        }

        const char = prompt[i];
        textarea.value += char;
        typedChars++;

        // Trigger input event for each character
        textarea.dispatchEvent(new Event('input', { bubbles: true }));

        // Log progress every 50 characters
        if (i % 50 === 0 && i > 0) {
          this.log(`⌨️ Typed ${i}/${prompt.length} characters ${context}`, 'DEBUG');
        }

        // Random delay between 10-50ms to simulate human typing
        const delay = Math.random() * 40 + 10;
        await this.delay(delay);
      }

      // Final events
      this.log(`📡 Triggering final change event ${context}`, 'DEBUG');
      textarea.dispatchEvent(new Event('change', { bubbles: true }));
      textarea.dispatchEvent(new Event('keyup', { bubbles: true }));

      await this.delay(500);

      // Verify typing worked
      const finalValue = textarea.value;
      this.log(`📝 Typing verification: expected=${prompt.length}, actual=${finalValue.length}, match=${finalValue === prompt} ${context}`, 'DEBUG');

      if (finalValue !== prompt) {
        this.log(`⚠️ WARNING: Typed text doesn't match prompt! ${context}`, 'WARNING');
        this.log(`📝 Expected: "${prompt.substring(0, 100)}..."`, 'DEBUG');
        this.log(`📝 Actual: "${finalValue.substring(0, 100)}..."`, 'DEBUG');
      } else {
        this.log(`✅ PROMPT TYPED SUCCESSFULLY (${typedChars} chars) ${context}`, 'SUCCESS');
      }

      this.logTiming(`Prompt typing ${context}`, startTime);

    } catch (error) {
      this.log(`❌ ERROR typing prompt ${context}: ${error.message}`, 'ERROR');
      throw error;
    }
  }

  async clickRunButton() {
    const selectors = [
      'span.label:contains("Run")',
      'button:contains("Run")',
      '[aria-label*="Run"]',
      '.run-button'
    ];

    // Custom selector for text content
    const runButton = await this.waitForElementWithText('span', 'Run', 5000);
    
    if (!runButton) {
      throw new Error('Could not find Run button');
    }

    this.log('Clicking Run button');
    runButton.click();
    await this.delay(1000);
  }

  async waitAndDownload(promptData, promptIndex = 0, retryCount = 0) {
    const context = `[P${promptIndex + 1}:A${retryCount + 1}]`;

    this.log(`💾 WAITING FOR DOWNLOAD BUTTON ${context}`, 'WAIT');

    const downloadButton = await this.waitForDownloadButton(promptIndex, retryCount);

    if (!downloadButton) {
      throw new Error(`Download button did not appear within timeout ${context}`);
    }

    // Prepare filename before download
    const timestamp = new Date();
    const formattedTimestamp = this.formatTimestamp(timestamp);
    const promptId = promptData.id || 'unknown';
    const suggestedFilename = `${formattedTimestamp}_${promptId}.png`;

    this.log(`💾 Download button found, clicking... (suggested: ${suggestedFilename}) ${context}`, 'CLICK');

    // Send download info to background script BEFORE clicking
    chrome.runtime.sendMessage({
      action: 'downloadStarted',
      promptData: promptData,
      timestamp: timestamp.toISOString(),
      suggestedFilename: suggestedFilename
    });

    // Small delay to ensure message is processed
    await this.delay(500);

    // Now click the download button
    downloadButton.click();

    // Wait longer for download to actually start and complete
    this.log(`⏳ Waiting for download to complete... ${context}`, 'WAIT');
    await this.delay(5000); // Increased wait time

    // Verify download button disappears (indicating download completed)
    await this.waitForDownloadToComplete(promptIndex, retryCount);
  }

  async waitAndSaveToDrive(promptData, promptIndex = 0, retryCount = 0) {
    const context = `[P${promptIndex + 1}:A${retryCount + 1}]`;

    this.log(`☁️ WAITING FOR GOOGLE DRIVE BUTTON ${context}`, 'WAIT');

    const driveButton = await this.waitForDriveButton(promptIndex, retryCount);

    if (!driveButton) {
      throw new Error(`Google Drive button did not appear within timeout ${context}`);
    }

    this.log(`☁️ Google Drive button found, clicking... ${context}`, 'CLICK');

    // Click the Google Drive export button
    driveButton.click();

    // Wait for the save to complete
    this.log(`⏳ Waiting for Google Drive save to complete... ${context}`, 'WAIT');
    await this.delay(3000);

    // Verify save completed (button state change or notification)
    await this.waitForDriveSaveToComplete(promptIndex, retryCount);

    this.log(`✅ Google Drive save completed ${context}`, 'SUCCESS');
  }

  async waitForDriveButton(promptIndex = 0, retryCount = 0) {
    const context = `[P${promptIndex + 1}:A${retryCount + 1}]`;
    const startTime = Date.now();
    let lastLogTime = 0;
    let checkCount = 0;

    this.log(`⏳ WAITING FOR GOOGLE DRIVE BUTTON (timeout: ${this.downloadTimeout/1000}s) ${context}`, 'WAIT');
    this.logDOMState(`DRIVE_WAIT_START_${context}`);

    while (Date.now() - startTime < this.downloadTimeout) {
      if (!this.isRunning) {
        this.log(`🛑 Drive button wait interrupted by stop signal ${context}`, 'WARNING');
        return null;
      }

      checkCount++;
      const elapsed = Date.now() - startTime;

      // Log progress every 10 seconds
      if (elapsed - lastLogTime > 10000) {
        this.log(`⏳ Still waiting for Google Drive button... (${Math.round(elapsed/1000)}s elapsed, ${checkCount} checks) ${context}`, 'WAIT');
        this.logDOMState(`DRIVE_WAIT_${Math.round(elapsed/1000)}s`);
        lastLogTime = elapsed;
      }

      // Look for Google Drive export icon
      const driveIcons = document.querySelectorAll('mat-icon[data-mat-icon-type="font"]');
      this.log(`🔍 Found ${driveIcons.length} mat-icons for drive search ${context}`, 'DEBUG');

      for (let i = 0; i < driveIcons.length; i++) {
        const icon = driveIcons[i];
        const text = icon.textContent.trim();
        this.log(`🔍 Mat-icon ${i + 1}: text="${text}", visible=${icon.offsetParent !== null} ${context}`, 'DEBUG');

        if (text === 'drive_export') {
          const button = icon.closest('button') || icon;
          this.log(`✅ GOOGLE DRIVE BUTTON FOUND! ${context}`, 'SUCCESS');
          this.log(`📏 Button properties: tag=${button.tagName}, disabled=${button.disabled}, visible=${button.offsetParent !== null}`, 'DEBUG');
          this.logTiming(`Google Drive button detection ${context}`, startTime);
          return button;
        }
      }

      await this.delay(2000); // Check every 2 seconds
    }

    this.logTiming(`Google Drive button timeout ${context}`, startTime);
    this.log(`❌ GOOGLE DRIVE BUTTON TIMEOUT - no drive button found after ${checkCount} checks ${context}`, 'ERROR');
    this.logDOMState(`DRIVE_TIMEOUT_${context}`);
    return null;
  }

  async waitForDownloadToComplete(promptIndex = 0, retryCount = 0) {
    const context = `[P${promptIndex + 1}:A${retryCount + 1}]`;

    this.log(`🔍 Verifying download completion... ${context}`, 'DEBUG');

    // Wait for download button to disappear or change state
    const maxWait = 30000; // 30 seconds max wait
    const startTime = Date.now();

    while (Date.now() - startTime < maxWait) {
      if (!this.isRunning) return;

      // Check if download button still exists and is in the same state
      const downloadButton = document.querySelector('mat-icon[data-mat-icon-type="font"]');
      const isDownloadButton = downloadButton && downloadButton.textContent.trim() === 'download';

      if (!isDownloadButton) {
        this.log(`✅ Download appears to be complete (button changed/disappeared) ${context}`, 'SUCCESS');
        return;
      }

      await this.delay(1000); // Check every second
    }

    this.log(`⚠️ Download completion timeout - proceeding anyway ${context}`, 'WARNING');
  }

  async waitForDriveSaveToComplete(promptIndex = 0, retryCount = 0) {
    const context = `[P${promptIndex + 1}:A${retryCount + 1}]`;

    this.log(`🔍 Verifying Google Drive save completion... ${context}`, 'DEBUG');

    // Wait for any success indicators or button state changes
    const maxWait = 15000; // 15 seconds max wait
    const startTime = Date.now();

    while (Date.now() - startTime < maxWait) {
      if (!this.isRunning) return;

      // Look for success notifications or state changes
      const notifications = document.querySelectorAll('[class*="notification"], [class*="toast"], [class*="snackbar"]');
      for (const notification of notifications) {
        const text = notification.textContent.toLowerCase();
        if (text.includes('saved') || text.includes('exported') || text.includes('drive')) {
          this.log(`✅ Google Drive save confirmed by notification: "${notification.textContent}" ${context}`, 'SUCCESS');
          return;
        }
      }

      await this.delay(1000); // Check every second
    }

    this.log(`⚠️ Google Drive save completion timeout - proceeding anyway ${context}`, 'WARNING');
  }

  formatTimestamp(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}${month}${day}_${hours}${minutes}${seconds}`;
  }

  async waitForDownloadButton(promptIndex = 0, retryCount = 0) {
    const context = `[P${promptIndex + 1}:A${retryCount + 1}]`;
    const startTime = Date.now();
    let lastLogTime = 0;
    let checkCount = 0;

    this.log(`⏳ WAITING FOR DOWNLOAD BUTTON (timeout: ${this.downloadTimeout/1000}s) ${context}`, 'WAIT');
    this.logDOMState(`DOWNLOAD_WAIT_START_${context}`);

    while (Date.now() - startTime < this.downloadTimeout) {
      if (!this.isRunning) {
        this.log(`🛑 Download wait interrupted by stop signal ${context}`, 'WARNING');
        return null;
      }

      checkCount++;
      const elapsed = Date.now() - startTime;

      // Log progress every 10 seconds
      if (elapsed - lastLogTime > 10000) {
        this.log(`⏳ Still waiting for download button... (${Math.round(elapsed/1000)}s elapsed, ${checkCount} checks) ${context}`, 'WAIT');
        this.logDOMState(`DOWNLOAD_WAIT_${Math.round(elapsed/1000)}s`);
        lastLogTime = elapsed;
      }

      // Method 1: Look for download icon by text content
      const downloadIcons = document.querySelectorAll('mat-icon[data-mat-icon-type="font"]');
      this.log(`🔍 Found ${downloadIcons.length} mat-icons with font type ${context}`, 'DEBUG');

      for (let i = 0; i < downloadIcons.length; i++) {
        const icon = downloadIcons[i];
        const text = icon.textContent.trim();
        this.log(`🔍 Mat-icon ${i + 1}: text="${text}", visible=${icon.offsetParent !== null} ${context}`, 'DEBUG');

        if (text === 'download') {
          const button = icon.closest('button') || icon;
          this.log(`✅ DOWNLOAD BUTTON FOUND (method 1)! ${context}`, 'SUCCESS');
          this.log(`📏 Button properties: tag=${button.tagName}, disabled=${button.disabled}, visible=${button.offsetParent !== null}`, 'DEBUG');
          this.logTiming(`Download button detection ${context}`, startTime);
          return button;
        }
      }

      // Method 2: Look for any mat-icon with download text
      const allMatIcons = document.querySelectorAll('mat-icon');
      this.log(`🔍 Found ${allMatIcons.length} total mat-icons ${context}`, 'DEBUG');

      for (let i = 0; i < allMatIcons.length; i++) {
        const element = allMatIcons[i];
        const text = element.textContent.trim();

        if (text === 'download') {
          const button = element.closest('button') || element;
          this.log(`✅ DOWNLOAD BUTTON FOUND (method 2)! ${context}`, 'SUCCESS');
          this.log(`📏 Button properties: tag=${button.tagName}, disabled=${button.disabled}, visible=${button.offsetParent !== null}`, 'DEBUG');
          this.logTiming(`Download button detection ${context}`, startTime);
          return button;
        }
      }

      // Method 3: Check for other possible download indicators
      const downloadButtons = document.querySelectorAll('button[aria-label*="download" i], [title*="download" i]');
      if (downloadButtons.length > 0) {
        this.log(`✅ DOWNLOAD BUTTON FOUND (method 3 - aria-label)! Found ${downloadButtons.length} buttons ${context}`, 'SUCCESS');
        this.logTiming(`Download button detection ${context}`, startTime);
        return downloadButtons[0];
      }

      // Log current page state every 30 seconds
      if (elapsed > 0 && elapsed % 30000 < 2000) {
        this.log(`📊 PAGE STATE SNAPSHOT at ${Math.round(elapsed/1000)}s:`, 'STATE');
        this.log(`   - Total buttons: ${document.querySelectorAll('button').length}`, 'STATE');
        this.log(`   - Total mat-icons: ${document.querySelectorAll('mat-icon').length}`, 'STATE');
        this.log(`   - Loading indicators: ${document.querySelectorAll('[class*="loading"], [class*="Loading"]').length}`, 'STATE');
        this.log(`   - Error indicators: ${document.querySelectorAll('[class*="error"], [class*="Error"]').length}`, 'STATE');
      }

      await this.delay(2000); // Check every 2 seconds
    }

    this.logTiming(`Download button timeout ${context}`, startTime);
    this.log(`❌ DOWNLOAD BUTTON TIMEOUT - no download button found after ${checkCount} checks ${context}`, 'ERROR');
    this.logDOMState(`DOWNLOAD_TIMEOUT_${context}`);
    return null;
  }

  async waitForElement(selector, timeout = 10000, context = '') {
    const startTime = Date.now();
    let checkCount = 0;

    this.log(`⏳ WAITING FOR ELEMENT: "${selector}" (timeout: ${timeout}ms) ${context}`, 'WAIT');

    while (Date.now() - startTime < timeout) {
      checkCount++;
      const element = document.querySelector(selector);

      if (element) {
        this.log(`✅ ELEMENT FOUND: "${selector}" after ${checkCount} checks (${Date.now() - startTime}ms) ${context}`, 'SUCCESS');
        this.log(`📏 Element properties: tag=${element.tagName}, id="${element.id}", class="${element.className}", visible=${element.offsetParent !== null}`, 'DEBUG');
        return element;
      }

      // Log progress for long waits
      const elapsed = Date.now() - startTime;
      if (elapsed > 5000 && checkCount % 50 === 0) {
        this.log(`⏳ Still waiting for "${selector}"... (${elapsed}ms, ${checkCount} checks) ${context}`, 'DEBUG');
      }

      await this.delay(100);
    }

    this.log(`❌ ELEMENT NOT FOUND: "${selector}" after ${checkCount} checks (${timeout}ms timeout) ${context}`, 'ERROR');
    return null;
  }

  async waitForElementWithText(tagName, text, timeout = 10000) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      const elements = document.querySelectorAll(tagName);
      for (const element of elements) {
        if (element.textContent.trim() === text) {
          return element;
        }
      }
      await this.delay(100);
    }
    
    return null;
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  sendStatusUpdate(status, message, data = {}) {
    chrome.runtime.sendMessage({
      action: 'statusUpdate',
      status: status,
      message: message,
      data: {
        currentIndex: this.currentPromptIndex,
        totalPrompts: this.prompts.length,
        ...data
      }
    });
  }
}

// Prevent multiple initialization
if (!window.imageAutomatorInitialized) {
  window.imageAutomatorInitialized = true;

  // Initialize the automator when the page loads
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      new ImageAutomator();
    });
  } else {
    new ImageAutomator();
  }
}
