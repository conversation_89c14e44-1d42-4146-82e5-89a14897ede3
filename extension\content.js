// Gemini Auto Image Generator - Content Script
// Handles DOM interactions with Gemini's interface

class GeminiAutomator {
  constructor() {
    this.isRunning = false;
    this.currentPromptIndex = 0;
    this.prompts = [];
    this.maxRetries = 3;
    this.retryDelay = 2000;
    this.downloadTimeout = 60000; // 60 seconds timeout for download button
    
    this.setupMessageListener();
    this.log('Gemini Automator initialized');
  }

  log(message) {
    console.log(`[Gemini Automator] ${message}`);
  }

  setupMessageListener() {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // Keep message channel open for async response
    });
  }

  async handleMessage(request, sender, sendResponse) {
    try {
      switch (request.action) {
        case 'startAutomation':
          await this.startAutomation(request.prompts);
          sendResponse({ success: true });
          break;
        case 'stopAutomation':
          this.stopAutomation();
          sendResponse({ success: true });
          break;
        case 'getStatus':
          sendResponse({ 
            isRunning: this.isRunning,
            currentIndex: this.currentPromptIndex,
            totalPrompts: this.prompts.length
          });
          break;
        default:
          sendResponse({ success: false, error: 'Unknown action' });
      }
    } catch (error) {
      this.log(`Error handling message: ${error.message}`);
      sendResponse({ success: false, error: error.message });
    }
  }

  async startAutomation(prompts) {
    if (this.isRunning) {
      throw new Error('Automation is already running');
    }

    this.prompts = prompts;
    this.currentPromptIndex = 0;
    this.isRunning = true;

    this.log(`Starting automation with ${prompts.length} prompts`);
    this.sendStatusUpdate('started', 'Automation started');

    try {
      for (let i = 0; i < prompts.length; i++) {
        if (!this.isRunning) break;
        
        this.currentPromptIndex = i;
        const prompt = prompts[i];
        
        this.sendStatusUpdate('processing', `Processing prompt ${i + 1}/${prompts.length}`, {
          currentIndex: i,
          totalPrompts: prompts.length,
          prompt: prompt.visual_prompt
        });

        await this.processPrompt(prompt);
        
        // Small delay between prompts
        await this.delay(1000);
      }

      this.sendStatusUpdate('completed', 'All prompts processed successfully');
    } catch (error) {
      this.log(`Automation error: ${error.message}`);
      this.sendStatusUpdate('error', error.message);
    } finally {
      this.isRunning = false;
    }
  }

  stopAutomation() {
    this.isRunning = false;
    this.log('Automation stopped by user');
    this.sendStatusUpdate('stopped', 'Automation stopped by user');
  }

  async processPrompt(promptData) {
    const prompt = promptData.visual_prompt;
    this.log(`Processing prompt: ${prompt.substring(0, 50)}...`);

    let retryCount = 0;
    while (retryCount < this.maxRetries) {
      try {
        // Step 1: Find and clear textarea
        const textarea = await this.findTextareaWithRetry();
        await this.clearTextarea(textarea);

        // Step 2: Type the prompt
        await this.typePrompt(textarea, prompt);

        // Step 3: Click run button
        await this.clickRunButtonWithRetry();

        // Step 4: Wait for download button and click it
        await this.waitAndDownloadWithRetry(promptData);

        // Step 5: Clear textarea for next prompt
        await this.clearTextarea(textarea);

        // Success - break out of retry loop
        break;

      } catch (error) {
        retryCount++;
        this.log(`Attempt ${retryCount} failed: ${error.message}`);

        if (retryCount >= this.maxRetries) {
          this.sendStatusUpdate('error', `Failed after ${this.maxRetries} attempts: ${error.message}`);
          throw error;
        }

        this.sendStatusUpdate('retrying', `Retrying in ${this.retryDelay/1000}s... (${retryCount}/${this.maxRetries})`);
        await this.delay(this.retryDelay);

        // Try to recover by refreshing the page state
        await this.attemptRecovery();
      }
    }
  }

  async findTextareaWithRetry() {
    for (let attempt = 0; attempt < 3; attempt++) {
      try {
        const textarea = await this.findTextarea();
        if (textarea) return textarea;
      } catch (error) {
        this.log(`Textarea search attempt ${attempt + 1} failed: ${error.message}`);
        if (attempt < 2) await this.delay(1000);
      }
    }
    throw new Error('Could not find textarea after multiple attempts');
  }

  async clickRunButtonWithRetry() {
    for (let attempt = 0; attempt < 3; attempt++) {
      try {
        await this.clickRunButton();
        return;
      } catch (error) {
        this.log(`Run button click attempt ${attempt + 1} failed: ${error.message}`);
        if (attempt < 2) await this.delay(1000);
      }
    }
    throw new Error('Could not click run button after multiple attempts');
  }

  async waitAndDownloadWithRetry(promptData) {
    for (let attempt = 0; attempt < 2; attempt++) {
      try {
        await this.waitAndDownload(promptData);
        return;
      } catch (error) {
        this.log(`Download attempt ${attempt + 1} failed: ${error.message}`);
        if (attempt < 1) {
          this.log('Retrying download detection...');
          await this.delay(5000); // Wait longer before retry
        }
      }
    }
    throw new Error('Could not complete download after multiple attempts');
  }

  async attemptRecovery() {
    this.log('Attempting recovery...');

    try {
      // Clear any existing content
      const textarea = document.querySelector('textarea');
      if (textarea) {
        textarea.value = '';
        textarea.dispatchEvent(new Event('input', { bubbles: true }));
      }

      // Wait for page to stabilize
      await this.delay(2000);

    } catch (error) {
      this.log(`Recovery attempt failed: ${error.message}`);
    }
  }

  async findTextarea() {
    const selectors = [
      'textarea[aria-label="Enter a prompt to generate an image"]',
      'textarea[formcontrolname="prompt"]',
      'textarea.textarea',
      'textarea[placeholder="Describe your image"]'
    ];

    for (const selector of selectors) {
      const element = await this.waitForElement(selector, 5000);
      if (element) {
        this.log(`Found textarea with selector: ${selector}`);
        return element;
      }
    }

    throw new Error('Could not find prompt textarea');
  }

  async clearTextarea(textarea) {
    textarea.focus();
    textarea.select();
    textarea.value = '';
    
    // Trigger input events to ensure Angular detects the change
    textarea.dispatchEvent(new Event('input', { bubbles: true }));
    textarea.dispatchEvent(new Event('change', { bubbles: true }));
    
    await this.delay(500);
  }

  async typePrompt(textarea, prompt) {
    textarea.focus();
    
    // Type character by character to simulate human typing
    for (let i = 0; i < prompt.length; i++) {
      if (!this.isRunning) break;
      
      textarea.value += prompt[i];
      textarea.dispatchEvent(new Event('input', { bubbles: true }));
      
      // Random delay between 10-50ms to simulate human typing
      await this.delay(Math.random() * 40 + 10);
    }
    
    textarea.dispatchEvent(new Event('change', { bubbles: true }));
    await this.delay(500);
  }

  async clickRunButton() {
    const selectors = [
      'span.label:contains("Run")',
      'button:contains("Run")',
      '[aria-label*="Run"]',
      '.run-button'
    ];

    // Custom selector for text content
    const runButton = await this.waitForElementWithText('span', 'Run', 5000);
    
    if (!runButton) {
      throw new Error('Could not find Run button');
    }

    this.log('Clicking Run button');
    runButton.click();
    await this.delay(1000);
  }

  async waitAndDownload(promptData) {
    this.log('Waiting for download button...');
    
    const downloadButton = await this.waitForDownloadButton();
    
    if (!downloadButton) {
      throw new Error('Download button did not appear within timeout');
    }

    this.log('Download button found, clicking...');
    downloadButton.click();
    
    // Send download info to background script for file naming
    chrome.runtime.sendMessage({
      action: 'downloadStarted',
      promptData: promptData,
      timestamp: new Date().toISOString()
    });

    await this.delay(2000); // Wait for download to start
  }

  async waitForDownloadButton() {
    const selectors = [
      'mat-icon:contains("download")',
      '[data-mat-icon-type="font"]:contains("download")',
      '.material-icons:contains("download")',
      'button[aria-label*="download" i]'
    ];

    const startTime = Date.now();
    
    while (Date.now() - startTime < this.downloadTimeout) {
      if (!this.isRunning) return null;
      
      // Look for download icon by text content
      const downloadIcon = document.querySelector('mat-icon[data-mat-icon-type="font"]');
      if (downloadIcon && downloadIcon.textContent.trim() === 'download') {
        return downloadIcon.closest('button') || downloadIcon;
      }

      // Alternative: look for any element containing "download" text
      const elements = document.querySelectorAll('*');
      for (const element of elements) {
        if (element.textContent.trim() === 'download' && 
            (element.tagName === 'MAT-ICON' || element.closest('button'))) {
          return element.closest('button') || element;
        }
      }

      await this.delay(1000);
    }

    return null;
  }

  async waitForElement(selector, timeout = 10000) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      const element = document.querySelector(selector);
      if (element) return element;
      await this.delay(100);
    }
    
    return null;
  }

  async waitForElementWithText(tagName, text, timeout = 10000) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      const elements = document.querySelectorAll(tagName);
      for (const element of elements) {
        if (element.textContent.trim() === text) {
          return element;
        }
      }
      await this.delay(100);
    }
    
    return null;
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  sendStatusUpdate(status, message, data = {}) {
    chrome.runtime.sendMessage({
      action: 'statusUpdate',
      status: status,
      message: message,
      data: {
        currentIndex: this.currentPromptIndex,
        totalPrompts: this.prompts.length,
        ...data
      }
    });
  }
}

// Initialize the automator when the page loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new GeminiAutomator();
  });
} else {
  new GeminiAutomator();
}
