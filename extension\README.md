# Gemini Auto Image Generator

A Chrome extension that automates image generation on Gemini's platform by processing JSON files and handling the complete workflow automatically.

## Features

- **Automated Workflow**: Processes JSON files with image prompts and automates the entire generation process
- **Batch Processing**: Handles multiple prompts in sequence with progress tracking
- **Smart Download Management**: Automatically downloads generated images with timestamp naming
- **Error Recovery**: Robust error handling with retry mechanisms and recovery attempts
- **Dark Theme UI**: Modern, clean interface with real-time status updates
- **Activity Logging**: Comprehensive logging of all automation activities

## Installation

1. Download or clone this extension folder
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the extension folder
5. The extension icon should appear in your Chrome toolbar

## Usage

### Step 1: Prepare Your JSON File

Create a JSON file with the following format:

```json
[
  {
    "id": "unique_identifier",
    "ori_text": "original text content", 
    "duration": "time_duration",
    "visual_prompt": "detailed image generation prompt"
  }
]
```

**Important**: Only the `visual_prompt` field is required for automation. See `sample-prompts.json` for examples.

### Step 2: Open Gemini

1. Navigate to [gemini.google.com](https://gemini.google.com)
2. Make sure you're on a page that supports image generation
3. Ensure the page is fully loaded

### Step 3: Run the Extension

1. Click the extension icon in your Chrome toolbar
2. Upload your JSON file using the file picker or drag-and-drop
3. Review the loaded prompts count
4. Click "Start Automation"
5. Monitor progress in real-time

### Step 4: Monitor Progress

The extension will automatically:
- Type each prompt into Gemini's textarea
- Click the "Run" button to generate images
- Wait for generation to complete
- Download images with timestamp naming (`YYYYMMDD_HHMMSS_promptid.png`)
- Move to the next prompt

## File Structure

```
extension/
├── manifest.json          # Extension configuration
├── popup.html            # Extension popup interface
├── popup.css             # Popup styling (dark theme)
├── popup.js              # Popup logic and UI interactions
├── content.js            # DOM manipulation and automation
├── background.js         # File processing and coordination
├── sample-prompts.json   # Example JSON file
├── README.md            # This documentation
└── icons/               # Extension icons (add your own)
```

## JSON Format Details

### Required Fields
- `visual_prompt`: The text prompt for image generation

### Optional Fields
- `id`: Unique identifier (used in filename)
- `ori_text`: Original text content
- `duration`: Time duration information

### Example
```json
{
  "id": "sunset_landscape",
  "ori_text": "Beautiful nature scene",
  "duration": "30s",
  "visual_prompt": "A serene mountain landscape at sunset with golden light reflecting on a crystal-clear lake"
}
```

## Troubleshooting

### Common Issues

**Extension not working:**
- Ensure you're on gemini.google.com
- Refresh the Gemini page and try again
- Check that the page is fully loaded

**Prompts not being typed:**
- Verify the textarea is visible and accessible
- Try clicking in the prompt area manually first
- Check browser console for error messages

**Downloads not starting:**
- Ensure pop-ups and downloads are allowed for gemini.google.com
- Check Chrome's download settings
- Verify images are actually being generated

**Automation stops unexpectedly:**
- Check the activity log for error messages
- Try reducing the number of prompts in your JSON
- Ensure stable internet connection

### Error Recovery

The extension includes automatic error recovery:
- **Retry Mechanism**: Failed operations are retried up to 3 times
- **Element Detection**: Smart waiting for page elements to load
- **Timeout Handling**: Prevents infinite waiting with configurable timeouts
- **Recovery Attempts**: Automatic page state recovery between retries

## Technical Details

### Permissions Required
- `activeTab`: Access to current tab for DOM manipulation
- `downloads`: Managing downloaded files
- `storage`: Storing download history and settings
- `scripting`: Injecting content scripts
- Host permission for `gemini.google.com`

### Browser Compatibility
- Chrome 88+ (Manifest V3 support required)
- Chromium-based browsers (Edge, Brave, etc.)

## Development

### Building from Source
1. Clone the repository
2. No build process required - pure JavaScript
3. Load as unpacked extension in Chrome

### Customization
- Modify `popup.css` for UI theming
- Adjust timeouts and retry counts in `content.js`
- Add new features in respective script files

## Support

For issues, feature requests, or questions:
1. Check the troubleshooting section above
2. Review browser console for error messages
3. Ensure you're using the latest version of Chrome
4. Verify Gemini's interface hasn't changed

## Version History

### v1.0.0
- Initial release
- Basic automation workflow
- Dark theme UI
- Error handling and recovery
- Download management with timestamps
- Activity logging

## License

This extension is provided as-is for educational and automation purposes. Use responsibly and in accordance with Gemini's terms of service.
