# Icons Directory

This directory should contain the following icon files:
- icon16.png (16x16 pixels)
- icon32.png (32x32 pixels)
- icon48.png (48x48 pixels)
- icon128.png (128x128 pixels)

## Quick Icon Creation

### Option 1: Use Online Icon Generator
1. Go to any online icon generator (like favicon.io)
2. Create a simple icon with text "GA" or a gear symbol
3. Download in multiple sizes
4. Rename files to match the required names

### Option 2: Use Any Square Image
1. Find any square image (logo, photo, etc.)
2. Resize to the required dimensions
3. Save as PNG files with the correct names

### Option 3: Create Simple Colored Squares
1. Create solid colored squares in any image editor
2. Use a blue color (#0066cc) to match the extension theme
3. Save in the required sizes

## Temporary Solution
If you don't have icons ready, you can:
1. Create simple colored squares
2. Use the same image for all sizes
3. The extension will work fine with basic placeholder icons

The icons should represent the extension's purpose - perhaps a combination of:
- Gemini/AI symbol
- Image/photo icon
- Automation/gear symbol

You can create these icons using any image editor or online icon generator.
