{"manifest_version": 3, "name": "Gemini Auto Image Generator", "version": "1.0.0", "description": "Automate image generation on Gemini's platform by processing JSON files and handling the complete workflow automatically.", "permissions": ["activeTab", "downloads", "storage", "scripting"], "host_permissions": ["https://gemini.google.com/*"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["https://gemini.google.com/*"], "js": ["content.js"], "run_at": "document_idle"}], "action": {"default_popup": "popup.html", "default_title": "Gemini Auto Image Generator", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "web_accessible_resources": [{"resources": ["popup.html", "popup.css"], "matches": ["https://gemini.google.com/*"]}]}