{"manifest_version": 3, "name": "Auto Image Generator", "version": "1.0.0", "description": "Automate image generation by processing JSON files and handling the complete workflow automatically.", "permissions": ["activeTab", "downloads", "storage", "scripting"], "background": {"service_worker": "background.js"}, "action": {"default_popup": "popup.html", "default_title": "Auto Image Generator", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}