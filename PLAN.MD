# Chrome Extension Development Plan: Gemini Auto Image Generator

## Project Overview
Create a Chrome extension that automates the process of generating images using Gemini's image generator by reading JSON prompts and handling the complete workflow automatically.

## Core Features & Requirements

### 1. JSON File Processing
- **Input**: JSON file with structure:
  ```json
  {
    "id": "unique_identifier",
    "ori_text": "original text content",
    "duration": "time_duration",
    "visual_prompt": "detailed image generation prompt"
  }
  ```
- **Functionality**: Parse and extract `visual_prompt` values for automation

### 2. DOM Automation Workflow
**Step 1: Prompt Injection**
- Target element: `textarea[formcontrolname="prompt"]`
- Action: Auto-type the `visual_prompt` content

**Step 2: Generation Trigger**
- Target element: `span` with class `label` containing "Run" text
- Action: Programmatic click to start generation

**Step 3: Download Detection**
- Monitor for: `mat-icon` with `download` content
- Action: Wait for element appearance (generation complete)

**Step 4: File Download**
- Target: Download icon element
- Action: Click to download generated image
- Naming convention: `YYYYMMDD_HHMMSS_generated.png`

**Step 5: Cleanup & Loop**
- Clear textarea content
- Process next JSON entry
- Repeat until all prompts processed

### 3. User Interface Requirements
- **Theme**: Dark UI design
- **Type**: Minimal Viable Product (MVP)
- **Components**:
  - JSON file upload area
  - Progress indicator
  - Start/Stop controls
  - Status display
  - Download counter

## Technical Architecture

### Extension Structure
```
gemini-auto-generator/
├── manifest.json
├── popup/
│   ├── popup.html
│   ├── popup.css
│   └── popup.js
├── content/
│   └── content.js
├── background/
│   └── background.js
└── assets/
    └── icons/
```

### Key Technologies
- **Manifest V3** for Chrome extension
- **Content Scripts** for DOM manipulation
- **Background Scripts** for file processing
- **Popup Interface** for user controls
- **FileReader API** for JSON processing

## Implementation Flow

### Phase 1: Setup & Configuration
1. Create manifest.json with required permissions
2. Set up basic popup interface
3. Implement file upload functionality

### Phase 2: JSON Processing
1. Parse uploaded JSON file
2. Extract visual_prompt arrays
3. Queue management system

### Phase 3: DOM Automation
1. Content script injection
2. Element detection and interaction
3. Timing and wait mechanisms

### Phase 4: Download Management
1. Monitor download completion
2. File naming automation
3. Progress tracking

### Phase 5: UI Polish
1. Dark theme implementation
2. Status indicators
3. Error handling

## Required Permissions
```json
{
  "permissions": [
    "activeTab",
    "downloads",
    "storage"
  ],
  "host_permissions": [
    "https://gemini.google.com/*"
  ]
}
```

## Error Handling Strategy
- Network timeout detection
- DOM element availability checks
- JSON parsing validation
- Download failure recovery
- Rate limiting compliance

## Success Metrics
- Successful JSON file processing
- Accurate prompt injection
- Reliable generation triggering
- Automatic download completion
- Proper file naming
- Seamless looping functionality

## Deployment Considerations
- Chrome Web Store compliance
- User privacy protection
- Performance optimization
- Cross-browser compatibility (future)

## MVP Limitations
- Single JSON file processing
- Basic error reporting
- Simple progress indication
- Manual intervention for errors

This plan provides a comprehensive foundation for developing the Chrome extension with clear technical requirements and implementation steps.