<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini Auto Image Generator</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                    <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                    <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                </svg>
                <h1>Gemini Auto Generator</h1>
            </div>
            <div class="version">v1.0.0</div>
        </div>

        <!-- File Upload Section -->
        <div class="section">
            <h2>Upload JSON File</h2>
            <div class="file-upload-area" id="fileUploadArea">
                <div class="upload-icon">📁</div>
                <div class="upload-text">
                    <p>Drop JSON file here or <span class="upload-link">browse</span></p>
                    <small>Supports JSON files with visual_prompt fields</small>
                </div>
                <input type="file" id="fileInput" accept=".json" hidden>
            </div>
            <div class="file-info" id="fileInfo" style="display: none;">
                <div class="file-name" id="fileName"></div>
                <div class="file-details" id="fileDetails"></div>
            </div>
        </div>

        <!-- Status Section -->
        <div class="section">
            <h2>Status</h2>
            <div class="status-display">
                <div class="status-indicator" id="statusIndicator">
                    <div class="status-dot idle"></div>
                    <span id="statusText">Ready</span>
                </div>
                <div class="status-message" id="statusMessage">Upload a JSON file to begin</div>
            </div>
        </div>

        <!-- Progress Section -->
        <div class="section" id="progressSection" style="display: none;">
            <h2>Progress</h2>
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text">
                    <span id="progressText">0 / 0</span>
                    <span id="progressPercent">0%</span>
                </div>
            </div>
            <div class="current-prompt" id="currentPrompt" style="display: none;">
                <strong>Current:</strong>
                <span id="currentPromptText"></span>
            </div>
        </div>

        <!-- Controls Section -->
        <div class="section">
            <div class="controls">
                <button class="btn btn-primary" id="startBtn" disabled>
                    <span class="btn-icon">▶️</span>
                    Start Automation
                </button>
                <button class="btn btn-secondary" id="pauseBtn" disabled>
                    <span class="btn-icon">⏸️</span>
                    Pause
                </button>
                <button class="btn btn-danger" id="stopBtn" disabled>
                    <span class="btn-icon">⏹️</span>
                    Stop
                </button>
            </div>
        </div>

        <!-- Log Section -->
        <div class="section">
            <h2>Activity Log</h2>
            <div class="log-container" id="logContainer">
                <div class="log-entry">
                    <span class="log-time">--:--:--</span>
                    <span class="log-message">Extension ready</span>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="footer-links">
                <a href="#" id="helpLink">Help</a>
                <a href="#" id="settingsLink">Settings</a>
            </div>
            <div class="footer-text">
                Made for Gemini automation
            </div>
        </div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
